<!-- Add viewport meta tag for mobile responsiveness -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

<!-- Chat Toggle Button -->
<div id="chat-toggle" style="position: fixed; bottom: 24px; right: 24px; z-index: 9999;">
  <button onclick="toggleChat()" style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); color: #fff; border: none; border-radius: 50px; padding: 16px 24px; font-size: 15px; font-weight: 700; cursor: pointer; box-shadow: 0 12px 40px rgba(65, 105, 225, 0.4); white-space: nowrap; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); letter-spacing: 0.8px; text-transform: uppercase; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif; backdrop-filter: blur(10px); position: relative; overflow: hidden;">
    <span style="position: relative; z-index: 2;">hire me</span>
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%); opacity: 0; transition: opacity 0.3s ease;"></div>
  </button>
</div>

<!-- Chat Box -->
<div id="chat-box" style="display: none; position: fixed; bottom: 100px; right: 24px; width: 380px; height: 520px; background: #ffffff; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(65, 105, 225, 0.1); z-index: 99999; overflow: hidden; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2);">
  <!-- Chat Header -->
  <div style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); color: white; padding: 20px; font-weight: 600; display: flex; justify-content: space-between; align-items: center; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);"></div>
    <div style="display: flex; align-items: center; gap: 12px; position: relative; z-index: 2;">
      <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px);">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </div>
      <div>
        <div style="font-size: 16px; font-weight: 700; margin-bottom: 2px;">AI Interview Assistant</div>
        <div style="font-size: 12px; opacity: 0.9; font-weight: 400;">Ready to help with your career</div>
      </div>
    </div>
    <div style="display: flex; align-items: center; gap: 8px; position: relative; z-index: 2;">
      <!-- Full-page view button -->
      <button onclick="openFullPageChat()" style="background: rgba(255,255,255,0.2); border: none; color: white; cursor: pointer; font-size: 14px; display: flex; align-items: center; padding: 8px; border-radius: 8px; transition: all 0.3s ease; backdrop-filter: blur(10px);" title="Open in full page">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
        </svg>
      </button>
      <!-- Refresh session button -->
      <button onclick="refreshSession()" style="background: rgba(255,255,255,0.2); border: none; color: white; cursor: pointer; font-size: 14px; display: flex; align-items: center; padding: 8px; border-radius: 8px; transition: all 0.3s ease; backdrop-filter: blur(10px);" title="Start new conversation">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M23 4v6h-6"></path>
          <path d="M1 20v-6h6"></path>
          <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
          <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Chat Messages -->
  <div id="chat-messages" style="height: calc(100% - 140px); padding: 20px; overflow-y: auto; background: linear-gradient(180deg, #fafbff 0%, #f8f9ff 100%); display: flex; flex-direction: column; gap: 12px; box-sizing: border-box; scroll-behavior: smooth;"></div>

  <!-- Chat Input Container -->
  <div id="chat-input-container" style="display: flex; padding: 20px; border-top: 1px solid rgba(65, 105, 225, 0.1); position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(180deg, #ffffff 0%, #fafbff 100%); z-index: 10; align-items: center; gap: 12px; min-height: 80px; box-sizing: border-box; backdrop-filter: blur(10px);">
    <!-- File upload button -->
    <label for="cv-upload" style="margin: 0; cursor: pointer; display: flex; align-items: center; background: linear-gradient(135deg, #f8f9ff 0%, #eef2ff 100%); border: 2px solid rgba(65, 105, 225, 0.1); padding: 12px; border-radius: 12px; transition: all 0.3s ease;" title="Upload CV/Resume">
      <input id="cv-upload" type="file" accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,.doc,.docx" style="display: none;" />
      <svg width="20" height="20" fill="none" stroke="#4169e1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
        <polyline points="14,2 14,8 20,8"/>
        <line x1="16" y1="13" x2="8" y2="13"/>
        <line x1="16" y1="17" x2="8" y2="17"/>
        <polyline points="10,9 9,9 8,9"/>
      </svg>
    </label>

    <!-- Message input -->
    <div style="flex: 1; position: relative;">
      <input id="user-input" type="text" placeholder="Ask about career opportunities, upload your CV, or start an interview..." style="width: 100%; border: 2px solid rgba(65, 105, 225, 0.1); outline: none; padding: 14px 16px; font-size: 14px; border-radius: 12px; background: #ffffff; transition: all 0.3s ease; box-sizing: border-box; font-family: inherit;" />
    </div>

    <!-- Send button -->
    <button onclick="sendMessage()" style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); border: none; cursor: pointer; padding: 12px; display: flex; align-items: center; border-radius: 12px; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(65, 105, 225, 0.3);" title="Send message">
      <svg width="20" height="20" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
        <line x1="22" y1="2" x2="11" y2="13"/>
        <polygon points="22 2 15 22 11 13 2 9 22 2"/>
      </svg>
    </button>
  </div>
</div>

<!-- Full Page Chat Modal -->
<div id="fullpage-chat-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.6); z-index: 999999; backdrop-filter: blur(15px); overflow: hidden; padding: 0;">
  <div id="fullpage-chat-container" style="width: 100%; height: 100%; background: #ffffff; display: flex; flex-direction: column; box-sizing: border-box;">
    <!-- Full page header -->
    <div class="full-page-header" style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); color: white; padding: 24px; display: flex; justify-content: space-between; align-items: center; position: relative; overflow: hidden;">
      <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);"></div>
      <div style="display: flex; align-items: center; gap: 16px; position: relative; z-index: 2;">
        <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px);">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
        </div>
        <div>
          <div style="font-size: 20px; font-weight: 700; margin-bottom: 4px;">AI Interview Assistant</div>
          <div style="font-size: 14px; opacity: 0.9; font-weight: 400;">Professional Career Interview Platform</div>
        </div>
      </div>
      <button onclick="closeFullPageChat()" style="background: rgba(255,255,255,0.2); border: none; color: white; cursor: pointer; padding: 12px; border-radius: 12px; transition: all 0.3s ease; backdrop-filter: blur(10px); position: relative; z-index: 2;" title="Close full page view">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- Full page messages -->
    <div id="fullpage-chat-messages" style="flex: 1; padding: 32px; overflow-y: auto; background: linear-gradient(180deg, #fafbff 0%, #f8f9ff 100%); display: flex; flex-direction: column; gap: 16px; scroll-behavior: smooth;"></div>

    <!-- Full page input -->
    <div class="full-page-input" style="padding: 24px; border-top: 1px solid rgba(65, 105, 225, 0.1); background: linear-gradient(180deg, #ffffff 0%, #fafbff 100%); display: flex; align-items: center; gap: 16px; backdrop-filter: blur(10px);">
      <label for="fullpage-cv-upload" style="cursor: pointer; display: flex; align-items: center; background: linear-gradient(135deg, #f8f9ff 0%, #eef2ff 100%); border: 2px solid rgba(65, 105, 225, 0.1); padding: 16px; border-radius: 16px; transition: all 0.3s ease;" title="Upload CV/Resume">
        <input id="fullpage-cv-upload" type="file" accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,.doc,.docx" style="display: none;" />
        <svg width="24" height="24" fill="none" stroke="#4169e1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
      </label>

      <div style="flex: 1; position: relative;">
        <input id="fullpage-user-input" type="text" placeholder="Ask detailed questions, discuss your career goals, or request interview practice..." style="width: 100%; border: 2px solid rgba(65, 105, 225, 0.1); outline: none; padding: 18px 20px; font-size: 16px; border-radius: 16px; background: #ffffff; transition: all 0.3s ease; box-sizing: border-box; font-family: inherit;" />
      </div>

      <button onclick="sendFullPageMessage()" style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); border: none; cursor: pointer; padding: 16px; display: flex; align-items: center; border-radius: 16px; transition: all 0.3s ease; box-shadow: 0 6px 20px rgba(65, 105, 225, 0.3);" title="Send message">
        <svg width="24" height="24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
          <line x1="22" y1="2" x2="11" y2="13"/>
          <polygon points="22 2 15 22 11 13 2 9 22 2"/>
        </svg>
      </button>
    </div>
  </div>
</div>

<script>
  // Make chat box responsive for mobile devices
  function adjustForMobile() {
    const chatBox = document.getElementById('chat-box');
    const chatToggle = document.getElementById('chat-toggle');
    const chatMessages = document.getElementById('chat-messages');
    const inputContainer = document.getElementById('chat-input-container');
    const fullPageContainer = document.getElementById('fullpage-chat-container');
    const fullPageHeader = document.querySelector('#fullpage-chat-modal .full-page-header');
    const fullPageInput = document.querySelector('#fullpage-chat-modal .full-page-input');

    if (window.innerWidth < 768) {
      // Mobile view - make chat box take up more screen space
      chatBox.style.width = '95%';
      chatBox.style.right = '2.5%';
      chatBox.style.left = 'auto';
      chatBox.style.bottom = '90px';
      chatBox.style.height = '70vh';
      chatBox.style.borderRadius = '20px';

      // Adjust the messages container height to fill available space
      if (chatMessages) {
        chatMessages.style.height = 'calc(100% - 160px)'; // Adjust for new header and input heights
        chatMessages.style.padding = '16px';
      }

      // Ensure input container is properly positioned with smaller buttons
      if (inputContainer) {
        inputContainer.style.padding = '12px';
        inputContainer.style.minHeight = '60px';
        inputContainer.style.borderRadius = '0 0 20px 20px';

        // Make buttons smaller on mobile
        const uploadLabel = inputContainer.querySelector('label[for="cv-upload"]');
        const sendButton = inputContainer.querySelector('button[onclick="sendMessage()"]');

        if (uploadLabel) {
          uploadLabel.style.padding = '8px';
          uploadLabel.style.borderRadius = '8px';
          const uploadSvg = uploadLabel.querySelector('svg');
          if (uploadSvg) {
            uploadSvg.style.width = '16px';
            uploadSvg.style.height = '16px';
          }
        }

        if (sendButton) {
          sendButton.style.padding = '8px';
          sendButton.style.borderRadius = '8px';
          const sendSvg = sendButton.querySelector('svg');
          if (sendSvg) {
            sendSvg.style.width = '16px';
            sendSvg.style.height = '16px';
          }
        }

        // Make input text smaller
        const userInput = inputContainer.querySelector('#user-input');
        if (userInput) {
          userInput.style.padding = '10px 12px';
          userInput.style.fontSize = '14px';
          userInput.style.borderRadius = '8px';
        }
      }

      // Keep toggle button position
      chatToggle.style.bottom = '20px';
      chatToggle.style.right = '20px';

      // Full-page modal responsive for mobile - keep full screen
      if (fullPageContainer) {
        fullPageContainer.style.maxWidth = '100%';
        fullPageContainer.style.margin = '0';
        fullPageContainer.style.padding = '0';
        fullPageContainer.style.borderRadius = '0';
        fullPageContainer.style.boxShadow = 'none';
        fullPageContainer.style.height = '100%';
        fullPageContainer.style.overflow = 'visible';
      }

      // Header and input styling for mobile
      if (fullPageHeader) {
        fullPageHeader.style.borderRadius = '0';
      }
      if (fullPageInput) {
        fullPageInput.style.borderRadius = '0';
      }
    } else if (window.innerWidth < 1024) {
      // Tablet view
      chatBox.style.width = '400px';
      chatBox.style.right = '24px';
      chatBox.style.left = 'auto';
      chatBox.style.bottom = '100px';
      chatBox.style.height = '550px';
      chatBox.style.borderRadius = '20px';

      // Reset messages container height
      if (chatMessages) {
        chatMessages.style.height = 'calc(100% - 140px)';
        chatMessages.style.padding = '20px';
      }

      // Reset input container and button sizes for tablet
      if (inputContainer) {
        inputContainer.style.padding = '18px';
        inputContainer.style.minHeight = '75px';
        inputContainer.style.borderRadius = '0 0 20px 20px';

        // Reset button sizes for tablet
        const uploadLabel = inputContainer.querySelector('label[for="cv-upload"]');
        const sendButton = inputContainer.querySelector('button[onclick="sendMessage()"]');

        if (uploadLabel) {
          uploadLabel.style.padding = '10px';
          uploadLabel.style.borderRadius = '10px';
          const uploadSvg = uploadLabel.querySelector('svg');
          if (uploadSvg) {
            uploadSvg.style.width = '18px';
            uploadSvg.style.height = '18px';
          }
        }

        if (sendButton) {
          sendButton.style.padding = '10px';
          sendButton.style.borderRadius = '10px';
          const sendSvg = sendButton.querySelector('svg');
          if (sendSvg) {
            sendSvg.style.width = '18px';
            sendSvg.style.height = '18px';
          }
        }

        // Reset input text for tablet
        const userInput = inputContainer.querySelector('#user-input');
        if (userInput) {
          userInput.style.padding = '12px 14px';
          userInput.style.fontSize = '14px';
          userInput.style.borderRadius = '10px';
        }
      }

      // Reset toggle button position
      chatToggle.style.bottom = '24px';
      chatToggle.style.right = '24px';

      // Full-page modal responsive for tablet - keep full screen with minimal padding
      if (fullPageContainer) {
        fullPageContainer.style.maxWidth = '100%';
        fullPageContainer.style.margin = '0';
        fullPageContainer.style.padding = '0';
        fullPageContainer.style.borderRadius = '0';
        fullPageContainer.style.boxShadow = 'none';
        fullPageContainer.style.height = '100%';
        fullPageContainer.style.overflow = 'visible';
      }

      // Header and input styling for tablet
      if (fullPageHeader) {
        fullPageHeader.style.borderRadius = '0';
      }
      if (fullPageInput) {
        fullPageInput.style.borderRadius = '0';
      }
    } else {
      // Desktop view - reset to default
      chatBox.style.width = '380px';
      chatBox.style.right = '24px';
      chatBox.style.left = 'auto';
      chatBox.style.bottom = '100px';
      chatBox.style.height = '520px';
      chatBox.style.borderRadius = '20px';

      // Reset messages container height
      if (chatMessages) {
        chatMessages.style.height = 'calc(100% - 140px)';
        chatMessages.style.padding = '20px';
      }

      // Reset input container and button sizes for desktop
      if (inputContainer) {
        inputContainer.style.padding = '20px';
        inputContainer.style.minHeight = '80px';
        inputContainer.style.borderRadius = '0 0 20px 20px';

        // Reset button sizes to default for desktop
        const uploadLabel = inputContainer.querySelector('label[for="cv-upload"]');
        const sendButton = inputContainer.querySelector('button[onclick="sendMessage()"]');

        if (uploadLabel) {
          uploadLabel.style.padding = '12px';
          uploadLabel.style.borderRadius = '12px';
          const uploadSvg = uploadLabel.querySelector('svg');
          if (uploadSvg) {
            uploadSvg.style.width = '20px';
            uploadSvg.style.height = '20px';
          }
        }

        if (sendButton) {
          sendButton.style.padding = '12px';
          sendButton.style.borderRadius = '12px';
          const sendSvg = sendButton.querySelector('svg');
          if (sendSvg) {
            sendSvg.style.width = '20px';
            sendSvg.style.height = '20px';
          }
        }

        // Reset input text for desktop
        const userInput = inputContainer.querySelector('#user-input');
        if (userInput) {
          userInput.style.padding = '14px 16px';
          userInput.style.fontSize = '14px';
          userInput.style.borderRadius = '12px';
        }
      }

      // Reset toggle button position
      chatToggle.style.bottom = '24px';
      chatToggle.style.right = '24px';

      // Full-page modal responsive for desktop - floating chat style
      if (fullPageContainer) {
        fullPageContainer.style.maxWidth = '900px';
        fullPageContainer.style.margin = '40px auto';
        fullPageContainer.style.padding = '0';
        fullPageContainer.style.borderRadius = '24px';
        fullPageContainer.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.3)';
        fullPageContainer.style.height = 'calc(100vh - 80px)';
        fullPageContainer.style.overflow = 'hidden';
      }

      // Header and input styling for desktop - rounded corners for floating effect
      if (fullPageHeader) {
        fullPageHeader.style.borderRadius = '24px 24px 0 0';
      }
      if (fullPageInput) {
        fullPageInput.style.borderRadius = '0 0 24px 24px';
      }
    }
  }

  // Call on page load and window resize
  window.addEventListener('load', adjustForMobile);
  window.addEventListener('resize', adjustForMobile);
</script>

<script>
  // Generate a new session ID for each page load
  let currentSessionId;
  let lastSentMessage = ""; // Store the last sent message for retry functionality

  // Initialize session when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    // Always generate a new UUID on page load
    currentSessionId = generateUUID();

    // Add event listener for Enter key - regular chat
    const userInput = document.getElementById('user-input');
    userInput.addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        sendMessage();
        e.preventDefault();
      }
    });

    // Add event listener for Enter key - full page chat
    const fullPageUserInput = document.getElementById('fullpage-user-input');
    if (fullPageUserInput) {
      fullPageUserInput.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
          sendFullPageMessage();
          e.preventDefault();
        }
      });
    }

    // Add event listener for input to handle phone numbers with + sign
    userInput.addEventListener('input', function() {
      // Force LTR for the input field to ensure + signs appear correctly
      this.setAttribute('dir', 'ltr');

      // If the input contains Arabic text but no + sign, use RTL
      if (containsArabic(this.value) && !this.value.includes('+')) {
        this.setAttribute('dir', 'rtl');
      }
    });

    // Add file upload listeners for both regular and full-page chat
    setupFileUploadListeners();
  });

  // Generate a proper UUID v4
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Function to refresh the session
  function refreshSession() {
    // Generate a new session ID
    currentSessionId = generateUUID();

    // Clear the chat messages in both regular and full-page views
    const chatMessages = document.getElementById('chat-messages');
    const fullPageChatMessages = document.getElementById('fullpage-chat-messages');

    chatMessages.innerHTML = '';
    if (fullPageChatMessages) {
      fullPageChatMessages.innerHTML = '';
    }

    // Add a system message to both views
    const newMessage = `New conversation started. Welcome to our AI Interview Assistant! I'm here to help with your career questions, CV review, or discuss opportunities. How can I assist you today?`;
    appendMessage(newMessage, 'bot');
    if (fullPageChatMessages) {
      appendFullPageMessage(newMessage, 'bot');
    }

    // Focus on the input field
    document.getElementById('user-input').focus();
  }

  // Full-page chat functions
  function openFullPageChat() {
    const modal = document.getElementById('fullpage-chat-modal');
    const fullPageMessages = document.getElementById('fullpage-chat-messages');
    const regularMessages = document.getElementById('chat-messages');

    // Show the modal
    modal.style.display = 'block';

    // Copy messages from regular chat to full-page chat
    fullPageMessages.innerHTML = regularMessages.innerHTML;

    // Focus on the full-page input
    setTimeout(() => {
      document.getElementById('fullpage-user-input').focus();
      fullPageMessages.scrollTop = fullPageMessages.scrollHeight;
    }, 100);
  }

  function closeFullPageChat() {
    const modal = document.getElementById('fullpage-chat-modal');
    const fullPageMessages = document.getElementById('fullpage-chat-messages');
    const regularMessages = document.getElementById('chat-messages');

    // Copy messages back from full-page to regular chat
    regularMessages.innerHTML = fullPageMessages.innerHTML;

    // Hide the modal
    modal.style.display = 'none';

    // Scroll regular chat to bottom
    setTimeout(() => {
      regularMessages.scrollTop = regularMessages.scrollHeight;
    }, 100);
  }

  function sendFullPageMessage() {
    const input = document.getElementById('fullpage-user-input');
    const message = input.value.trim();
    if (!message) return;

    // Add message to full-page chat
    appendFullPageMessage(message, 'user');
    input.value = '';

    // Also add to regular chat to keep them in sync
    appendMessage(message, 'user');

    // Send the message using the same API
    sendMessageToAPI(message);
  }

  function appendFullPageMessage(content, type) {
    const chat = document.getElementById('fullpage-chat-messages');
    const bubbleWrapper = document.createElement('div');
    bubbleWrapper.style.display = 'flex';
    bubbleWrapper.style.flexDirection = 'column';
    bubbleWrapper.style.alignItems = type === 'user' ? 'flex-end' : 'flex-start';

    const bubble = document.createElement('div');

    // Check if the content contains Arabic text
    const isArabic = containsArabic(content);

    // Fix phone numbers with + sign
    const phoneRegex = /(\+\d+)/g;
    const contentWithFixedPhones = content.replace(phoneRegex, '<span style="unicode-bidi: embed; direction: ltr;">$1</span>');

    // Set text direction based on content
    bubble.style.direction = isArabic ? 'rtl' : 'ltr';
    bubble.style.textAlign = isArabic ? 'right' : 'left';

    // Format content for better readability (convert line breaks to HTML)
    const formattedContent = contentWithFixedPhones.replace(/\n/g, '<br>');

    // Use innerHTML to allow our span tags and line breaks to work
    bubble.innerHTML = formattedContent;
    bubble.style.maxWidth = '85%';
    bubble.style.padding = '16px 20px';
    bubble.style.borderRadius = '20px';
    bubble.style.fontSize = '16px';
    bubble.style.lineHeight = '1.6';
    bubble.style.wordWrap = 'break-word';

    const timestamp = document.createElement('div');
    timestamp.textContent = getLocalizedTimestamp(isArabic ? 'Arabic' : 'English');
    timestamp.style.fontSize = '12px';
    timestamp.style.marginTop = '4px';
    timestamp.style.marginBottom = '6px';
    timestamp.style.color = '#777';
    timestamp.style.textAlign = isArabic ? 'right' : 'left';

    if (type === 'user') {
      bubble.style.background = 'linear-gradient(135deg, #4169e1 0%, #5a7bff 100%)';
      bubble.style.color = 'white';
      bubble.style.borderBottomRightRadius = '6px';
    } else {
      bubble.style.background = '#f0f2f5';
      bubble.style.color = '#333';
      bubble.style.borderBottomLeftRadius = '6px';
    }

    // Add animation class for smooth appearance
    bubble.className = 'message-bubble';

    bubbleWrapper.appendChild(bubble);
    bubbleWrapper.appendChild(timestamp);
    chat.appendChild(bubbleWrapper);
    chat.scrollTop = chat.scrollHeight;
  }

  // Setup file upload listeners for both regular and full-page chat
  function setupFileUploadListeners() {
    // Regular chat file upload
    const cvUpload = document.getElementById('cv-upload');
    if (cvUpload) {
      cvUpload.addEventListener('change', function(e) {
        handleFileUpload(e, false);
      });
    }

    // Full-page chat file upload
    const fullPageCvUpload = document.getElementById('fullpage-cv-upload');
    if (fullPageCvUpload) {
      fullPageCvUpload.addEventListener('change', function(e) {
        handleFileUpload(e, true);
      });
    }
  }

  function handleFileUpload(event, isFullPage) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a PDF, DOC, or DOCX file.');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB.');
      return;
    }

    // Send file directly to API without showing upload message
    sendFileToAPI(file, isFullPage);

    // Clear the input
    event.target.value = '';
  }

  // Initialize chat with welcome message when chat is first opened
  let chatInitialized = false;

  // Welcome message to show when chat is first opened
  const welcomeMessage = '👋 Welcome to LeadersScout AI Interview Assistant!\n\nI\'m here to help you with:\n• 📄 CV/Resume review and optimization\n• 💼 Career guidance and opportunities\n• 🎯 Interview preparation and practice\n• 📈 Professional development advice\n\nFeel free to upload your CV or ask me anything about your career journey!';

  function toggleChat() {
    const chatBox = document.getElementById('chat-box');
    const chat = document.getElementById('chat-messages');
    const isHidden = chatBox.style.display === 'none';

    chatBox.style.display = isHidden ? 'block' : 'none';

    if (isHidden) {
      // Apply mobile layout adjustments
      adjustForMobile();

      // Show welcome message if this is the first time opening the chat
      if (!chatInitialized) {
        appendMessage(welcomeMessage, 'bot');
        chatInitialized = true;
      }

      // Focus on the input field when chat is opened
      setTimeout(() => {
        document.getElementById('user-input').focus();
        chat.scrollTop = chat.scrollHeight;
      }, 100);
    }
  }

  // Function to detect if text contains Arabic characters
  function containsArabic(text) {
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicPattern.test(text);
  }

  // Function to get localized timestamp
  function getLocalizedTimestamp(language) {
    const now = new Date();
    if (language === 'Arabic') {
      // Use Arabic numerals and format
      return now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      return now.getHours().toString().padStart(2, '0') + ':' +
             now.getMinutes().toString().padStart(2, '0');
    }
  }

  function appendMessage(content, type) {
    const chat = document.getElementById('chat-messages');
    const bubbleWrapper = document.createElement('div');
    bubbleWrapper.style.display = 'flex';
    bubbleWrapper.style.flexDirection = 'column';
    bubbleWrapper.style.alignItems = type === 'user' ? 'flex-end' : 'flex-start';

    const bubble = document.createElement('div');

    // Check if the content contains Arabic text
    const isArabic = containsArabic(content);

    // Fix phone numbers with + sign
    // Look for patterns like digits with + sign and wrap them in a span with explicit LTR direction
    const phoneRegex = /(\+\d+)/g;
    const contentWithFixedPhones = content.replace(phoneRegex, '<span style="unicode-bidi: embed; direction: ltr;">$1</span>');

    // Set text direction based on content
    if (isArabic) {
      bubble.style.direction = 'rtl';
      bubble.style.textAlign = 'right';
      bubble.style.fontFamily = 'Arial, sans-serif'; // Better font for Arabic
    } else {
      bubble.style.direction = 'ltr';
      bubble.style.textAlign = 'left';
    }

    // Format content for better readability (convert line breaks to HTML)
    const formattedContent = contentWithFixedPhones.replace(/\n/g, '<br>');

    // Use innerHTML to allow our span tags and line breaks to work
    bubble.innerHTML = formattedContent;
    bubble.style.maxWidth = '80%';
    bubble.style.padding = '14px 18px';
    bubble.style.borderRadius = '18px';
    bubble.style.fontSize = '14px';
    bubble.style.lineHeight = '1.6';
    bubble.style.wordWrap = 'break-word';

    const timestamp = document.createElement('div');
    // Use localized timestamp for Arabic
    timestamp.textContent = getLocalizedTimestamp(isArabic ? 'Arabic' : 'English');
    timestamp.style.fontSize = '11px';
    timestamp.style.marginTop = '4px';
    timestamp.style.marginBottom = '6px';
    timestamp.style.color = '#777';

    // Align timestamp with text direction
    timestamp.style.textAlign = isArabic ? 'right' : 'left';

    if (type === 'user') {
      bubble.style.background = 'linear-gradient(135deg, #4169e1 0%, #5a7bff 100%)';
      bubble.style.color = 'white';
      bubble.style.borderBottomRightRadius = '6px';
    } else {
      bubble.style.background = '#f0f2f5';
      bubble.style.color = '#333';
      bubble.style.borderBottomLeftRadius = '6px';
    }

    // Add animation class for smooth appearance
    bubble.className = 'message-bubble';

    bubbleWrapper.appendChild(bubble);
    bubbleWrapper.appendChild(timestamp);
    chat.appendChild(bubbleWrapper);
    chat.scrollTop = chat.scrollHeight;

    // Add margin to bubble wrapper to prevent cutoff
    bubbleWrapper.style.marginBottom = '8px';
  }

  function showTypingAnimation() {
    // Show typing animation in regular chat
    const chat = document.getElementById('chat-messages');
    const typing = document.createElement('div');
    typing.id = 'typing-indicator';
    typing.style.alignSelf = 'flex-start';
    typing.style.background = '#f0f2f5';
    typing.style.maxWidth = '75%';
    typing.style.padding = '12px 16px';
    typing.style.borderRadius = '18px';
    typing.style.borderBottomLeftRadius = '6px';
    typing.style.fontSize = '14px';
    typing.style.marginBottom = '8px';

    // Also show in full-page chat if it's open
    const fullPageModal = document.getElementById('fullpage-chat-modal');
    const fullPageChat = document.getElementById('fullpage-chat-messages');
    let fullPageTyping = null;

    if (fullPageModal && fullPageModal.style.display === 'block' && fullPageChat) {
      fullPageTyping = document.createElement('div');
      fullPageTyping.id = 'fullpage-typing-indicator';
      fullPageTyping.style.alignSelf = 'flex-start';
      fullPageTyping.style.background = '#f0f2f5';
      fullPageTyping.style.maxWidth = '75%';
      fullPageTyping.style.padding = '12px 16px';
      fullPageTyping.style.borderRadius = '20px';
      fullPageTyping.style.borderBottomLeftRadius = '6px';
      fullPageTyping.style.fontSize = '16px';
      fullPageTyping.style.marginBottom = '12px';
    }

    // Create animated dots for regular chat
    const dotsContainer = document.createElement('div');
    dotsContainer.style.display = 'flex';
    dotsContainer.style.gap = '4px';

    // Create animated dots for full-page chat if needed
    let fullPageDotsContainer = null;
    if (fullPageTyping) {
      fullPageDotsContainer = document.createElement('div');
      fullPageDotsContainer.style.display = 'flex';
      fullPageDotsContainer.style.gap = '4px';
    }

    // Create three dots for both containers
    for (let i = 0; i < 3; i++) {
      // Regular chat dots
      const dot = document.createElement('div');
      dot.style.width = '6px';
      dot.style.height = '6px';
      dot.style.borderRadius = '50%';
      dot.style.background = '#666';
      dot.style.opacity = '0.7';
      dot.style.animation = `dotPulse 1.4s infinite ${i * 0.2}s`;
      dotsContainer.appendChild(dot);

      // Full-page chat dots
      if (fullPageDotsContainer) {
        const fullPageDot = document.createElement('div');
        fullPageDot.style.width = '8px';
        fullPageDot.style.height = '8px';
        fullPageDot.style.borderRadius = '50%';
        fullPageDot.style.background = '#666';
        fullPageDot.style.opacity = '0.7';
        fullPageDot.style.animation = `dotPulse 1.4s infinite ${i * 0.2}s`;
        fullPageDotsContainer.appendChild(fullPageDot);
      }
    }

    // Add animation keyframes and modern button styles
    if (!document.getElementById('dot-animation-style')) {
      const style = document.createElement('style');
      style.id = 'dot-animation-style';
      style.textContent = `
        @keyframes dotPulse {
          0%, 100% { transform: scale(0.7); opacity: 0.7; }
          50% { transform: scale(1.2); opacity: 1; }
        }
        
        @keyframes pulse {
          0% { box-shadow: 0 8px 25px rgba(65, 105, 225, 0.4); }
          50% { box-shadow: 0 8px 30px rgba(65, 105, 225, 0.6); }
          100% { box-shadow: 0 8px 25px rgba(65, 105, 225, 0.4); }
        }
        
        #chat-toggle button:hover {
          transform: translateY(-2px) scale(1.05);
          box-shadow: 0 12px 35px rgba(65, 105, 225, 0.6) !important;
          background: linear-gradient(135deg, #5a7bff 0%, #4169e1 100%) !important;
        }
        
        #chat-toggle button:active {
          transform: translateY(0) scale(1.02);
          box-shadow: 0 6px 20px rgba(65, 105, 225, 0.5) !important;
        }
        
        #chat-toggle button {
          animation: pulse 3s infinite;
        }

        /* Enhanced input focus effects */
        #user-input:focus, #fullpage-user-input:focus {
          border-color: #4169e1 !important;
          box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.1) !important;
          transform: translateY(-1px);
        }

        /* Upload button hover effects */
        label[for="cv-upload"]:hover, label[for="fullpage-cv-upload"]:hover {
          background: linear-gradient(135deg, #eef2ff 0%, #e0e8ff 100%) !important;
          border-color: rgba(65, 105, 225, 0.3) !important;
          transform: translateY(-1px);
        }

        /* Send button hover effects */
        button[onclick="sendMessage()"]:hover, button[onclick="sendFullPageMessage()"]:hover {
          background: linear-gradient(135deg, #5a7bff 0%, #4169e1 100%) !important;
          transform: translateY(-1px) scale(1.02);
          box-shadow: 0 6px 25px rgba(65, 105, 225, 0.4) !important;
        }

        /* Message bubble animations */
        .message-bubble {
          animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `;
      document.head.appendChild(style);
    }

    // Append dots to typing indicators
    typing.appendChild(dotsContainer);
    chat.appendChild(typing);
    chat.scrollTop = chat.scrollHeight;

    // Add to full-page chat if needed
    if (fullPageTyping && fullPageDotsContainer) {
      fullPageTyping.appendChild(fullPageDotsContainer);
      fullPageChat.appendChild(fullPageTyping);
      fullPageChat.scrollTop = fullPageChat.scrollHeight;
    }
  }

  function removeTypingAnimation() {
    // Remove from regular chat
    const typing = document.getElementById('typing-indicator');
    if (typing) typing.remove();

    // Remove from full-page chat
    const fullPageTyping = document.getElementById('fullpage-typing-indicator');
    if (fullPageTyping) fullPageTyping.remove();
  }

  // === API URL (initialize ONCE) ===
  const API_URL = 'http://127.0.0.1:8000/messages/';

  async function sendMessage(retryMessage = null) {
    const input = document.getElementById('user-input');
    let message = retryMessage || input.value.trim();
    if (!message) return;

    // Fix phone numbers with + sign before sending
    message = message.replace(/(\d+)\+/g, '+$1');

    // Store the message for potential retry
    lastSentMessage = message;

    // Only append user message and clear input if this is not a retry
    if (!retryMessage) {
      appendMessage(message, 'user');
      input.value = '';
    }

    // Send the message using the shared API function
    sendMessageToAPI(message);
  }

  // Shared function to send messages to API (used by both regular and full-page chat)
  async function sendMessageToAPI(message) {
    showTypingAnimation();

    try {
      // Ensure we have a session ID
      if (!currentSessionId) {
        currentSessionId = generateUUID();
      }

      // Get the current hostname
      const hostName = window.location.hostname;

      const formData = new FormData();
      formData.append('content', message);
      formData.append('session_id', currentSessionId);
      formData.append('host_name', hostName);

      const res = await fetch(API_URL, {
        method: 'POST',
        body: formData
      });

      if (!res.ok) {
        throw new Error(`Server responded with status: ${res.status}`);
      }

      const data = await res.json();

      // Calculate typing delay based on message length (min 1s, max 3s)
      const typingDelay = Math.min(Math.max(data.response.length * 10, 1000), 3000);

      setTimeout(() => {
        removeTypingAnimation();
        const response = data.response || 'No response';

        // Add response to both regular and full-page chat if full-page is open
        appendMessage(response, 'bot');
        const fullPageModal = document.getElementById('fullpage-chat-modal');
        if (fullPageModal && fullPageModal.style.display === 'block') {
          appendFullPageMessage(response, 'bot');
        }

        // Re-focus on appropriate input
        const activeInput = fullPageModal && fullPageModal.style.display === 'block'
          ? document.getElementById('fullpage-user-input')
          : document.getElementById('user-input');
        if (activeInput) activeInput.focus();
      }, typingDelay);
    } catch (err) {
      removeTypingAnimation();
      showConnectionError();
    }
  }

  // Function to send files to API
  async function sendFileToAPI(file, isFullPage) {
    try {
      // Ensure we have a session ID
      if (!currentSessionId) {
        currentSessionId = generateUUID();
      }

      // Get the current hostname
      const hostName = window.location.hostname;

      const formData = new FormData();
      formData.append('file', file);
      formData.append('session_id', currentSessionId);
      formData.append('host_name', hostName);

      const res = await fetch(API_URL, {
        method: 'POST',
        body: formData
      });

      if (!res.ok) {
        throw new Error(`Server responded with status: ${res.status}`);
      }

      const data = await res.json();
      const response = data.response || 'File uploaded successfully';

      // Add response ONLY ONCE - to both views to keep them synced
      appendMessage(response, 'bot');

      // Also add to full-page if it's open
      const fullPageModal = document.getElementById('fullpage-chat-modal');
      if (fullPageModal && fullPageModal.style.display === 'block') {
        appendFullPageMessage(response, 'bot');
      }

    } catch (err) {
      const errorMessage = 'Sorry, there was an error uploading your file. Please try again.';

      // Add error message ONLY ONCE - to both views to keep them synced
      appendMessage(errorMessage, 'bot');

      const fullPageModal = document.getElementById('fullpage-chat-modal');
      if (fullPageModal && fullPageModal.style.display === 'block') {
        appendFullPageMessage(errorMessage, 'bot');
      }
    }
  }

  function showConnectionError() {
    const chat = document.getElementById('chat-messages');
    const errorWrapper = document.createElement('div');
    errorWrapper.style.display = 'flex';
    errorWrapper.style.flexDirection = 'column';
    errorWrapper.style.alignItems = 'flex-start';
    errorWrapper.style.maxWidth = '75%';
    errorWrapper.style.marginBottom = '8px';

    const errorBubble = document.createElement('div');
    errorBubble.textContent = "Sorry, I couldn't connect to the server. Please check your connection.";
    errorBubble.style.background = '#ffdddd';
    errorBubble.style.color = '#d32f2f';
    errorBubble.style.padding = '8px 12px';
    errorBubble.style.borderRadius = '16px';
    errorBubble.style.borderBottomLeftRadius = '4px';
    errorBubble.style.fontSize = '14px';
    errorBubble.style.marginBottom = '8px';

    const retryBtn = document.createElement('button');
    retryBtn.textContent = "Try Again";
    retryBtn.style.background = '#0a0a23';
    retryBtn.style.color = 'white';
    retryBtn.style.border = 'none';
    retryBtn.style.borderRadius = '4px';
    retryBtn.style.padding = '6px 12px';
    retryBtn.style.cursor = 'pointer';
    retryBtn.style.fontSize = '12px';

    retryBtn.onclick = function() {
      errorWrapper.remove();
      sendMessage(lastSentMessage); // Retry with the last message
    };

    errorWrapper.appendChild(errorBubble);
    errorWrapper.appendChild(retryBtn);
    chat.appendChild(errorWrapper);
    chat.scrollTop = chat.scrollHeight;

    // Re-focus on input
    document.getElementById('user-input').focus();
  }

  // CV upload logic (PDF, DOC, DOCX)
  document.getElementById('cv-upload').addEventListener('change', async function(event) {
    const file = event.target.files[0];
    if (!file) return;
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const fileName = file.name.toLowerCase();
    const isAllowedType = allowedTypes.includes(file.type) || allowedExtensions.some(ext => fileName.endsWith(ext));
    if (!isAllowedType) {
      appendMessage('Only PDF or Word files (.pdf, .doc, .docx) are allowed.', 'bot');
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      appendMessage('File size must be less than 5MB.', 'bot');
      return;
    }
    appendMessage('Uploading your CV, please wait...', 'bot');
    const formData = new FormData();
    formData.append('file', file);
    formData.append('session_id', currentSessionId);
    try {
      const res = await fetch(API_URL, {
        method: 'POST',
        body: formData
      });
      if (!res.ok) {
        throw new Error('Upload failed');
      }
      const data = await res.json();
      appendMessage(data.response || 'Assessment complete.', 'bot');
    } catch (err) {
      appendMessage('Failed to upload CV. Please try again.', 'bot');
    }
  });
</script>
